"""
Nova Model Integration

This script integrates the Nova model (expanded epoch 17) with the conversation
handler and knowledge base.
"""

import os
import sys
import time
import torch
import logging
from typing import Dict, List, Any, Optional

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_brain_model.model.model_loader import load_model_from_checkpoint, load_tokenizer_from_file
from ai_brain_model.model.language_model import LanguageModel
from ai_brain_model.utils.improved_tokenizer import BPETokenizer
from ai_brain_model.utils.memory_optimization import MemoryOptimizer, KVCache
from ai_brain_model.utils.logging_config import get_logger, configure_logging
from nova_integration import NovaAI

# Configure logging
configure_logging(log_level="info", log_file="nova_model_integration.log")
logger = get_logger(__name__)

class NovaModelWrapper:
    """
    Wrapper for the Nova model (expanded epoch 17).

    This class loads the trained Nova model and provides a function
    that can be used with the NovaAI integration class.
    """

    def __init__(self,
                model_path: str = "output_expanded/nova_model_expanded_epoch_17.pt",
                tokenizer_path: str = "output_bpe/nova_bpe_tokenizer.json",
                use_memory_optimization: bool = True,
                max_sequence_length: int = 2048,
                temperature: float = 0.7,
                top_k: int = 50,
                top_p: float = 0.95,
                repetition_penalty: float = 1.1):
        """
        Initialize the Nova model wrapper.

        Args:
            model_path: Path to the trained model
            tokenizer_path: Path to the tokenizer
            use_memory_optimization: Whether to use memory optimization
            max_sequence_length: Maximum sequence length for the model
            temperature: Temperature for sampling
            top_k: Top-k for sampling
            top_p: Top-p (nucleus) for sampling
            repetition_penalty: Penalty for repetition
        """
        # Try to find model and tokenizer paths if not provided
        if model_path is None or tokenizer_path is None:
            self._find_model_files()
        else:
            self.model_path = model_path
            self.tokenizer_path = tokenizer_path

        self.use_memory_optimization = use_memory_optimization
        self.max_sequence_length = max_sequence_length
        self.temperature = temperature
        self.top_k = top_k
        self.top_p = top_p
        self.repetition_penalty = repetition_penalty

        # Load the model and tokenizer
        logger.info(f"Loading Nova model from {self.model_path}")
        self._load_model_and_tokenizer()

        # Initialize memory optimization if enabled
        if self.use_memory_optimization:
            logger.info("Initializing memory optimization")
            self._initialize_memory_optimization()

        # Initialize KV cache for faster generation
        self.kv_cache = KVCache(max_cache_size=10, max_seq_length=self.max_sequence_length)

        logger.info("Nova model wrapper initialized successfully")

    def _load_model_and_tokenizer(self) -> None:
        """Load the Nova model and tokenizer."""
        try:
            # Load tokenizer
            self.tokenizer = load_tokenizer_from_file(self.tokenizer_path)
            logger.info(f"Loaded tokenizer from {self.tokenizer_path}")

            # Load model
            self.model = load_model_from_checkpoint(self.model_path)
            self.model.eval()  # Set to evaluation mode
            logger.info(f"Loaded model from {self.model_path}")

        except Exception as e:
            logger.error(f"Error loading model and tokenizer: {str(e)}", exc_info=True)
            raise RuntimeError(f"Failed to load model and tokenizer: {str(e)}")

    def _initialize_memory_optimization(self) -> None:
        """Initialize memory optimization for the model."""
        try:
            # Create memory optimizer
            self.memory_optimizer = MemoryOptimizer()

            # Quantize the model to reduce memory usage
            self.model = self.memory_optimizer.quantize_model(
                self.model,
                quantization_type="dynamic"
            )

            # Clear GPU memory
            self.memory_optimizer.clear_gpu_memory()

            logger.info("Memory optimization applied to model")

        except Exception as e:
            logger.error(f"Error initializing memory optimization: {str(e)}")
            logger.warning("Continuing without memory optimization")

    def generate_text(self, prompt: str) -> str:
        """
        Generate text using the Nova model.

        Args:
            prompt: The prompt to generate text from

        Returns:
            The generated text
        """
        logger.info(f"🔄 Starting text generation for prompt: '{prompt[:50]}...'")
        try:
            # Tokenize the prompt
            logger.debug("📝 Tokenizing prompt...")
            input_ids = self.tokenizer.encode(prompt)
            logger.debug(f"📝 Tokenized to {len(input_ids)} tokens")

            # Truncate if needed
            if len(input_ids) > self.max_sequence_length - 100:  # Leave room for generation
                logger.debug(f"✂️ Truncating from {len(input_ids)} to {self.max_sequence_length - 100} tokens")
                input_ids = input_ids[-self.max_sequence_length + 100:]

            # Convert to tensor
            logger.debug("🔢 Converting to tensor...")
            input_tensor = torch.tensor([input_ids], dtype=torch.long)
            logger.debug(f"🔢 Tensor shape: {input_tensor.shape}")

            # Generate text
            logger.debug("🧠 Starting model generation...")
            with torch.no_grad():
                # Check which parameters are accepted by the generate method
                logger.debug("🔍 Inspecting model.generate parameters...")
                import inspect
                generate_params = inspect.signature(self.model.generate).parameters
                logger.debug(f"🔍 Available parameters: {list(generate_params.keys())}")

                # Build parameters dictionary based on what's accepted
                max_gen_length = min(100, self.max_sequence_length - len(input_ids))
                params = {
                    "input_ids": input_tensor,
                    "max_length": max_gen_length
                }
                logger.debug(f"🎯 Base params: input_ids shape={input_tensor.shape}, max_length={max_gen_length}")

                if "temperature" in generate_params:
                    params["temperature"] = self.temperature
                    logger.debug(f"🌡️ Added temperature: {self.temperature}")

                if "top_k" in generate_params:
                    params["top_k"] = self.top_k
                    logger.debug(f"🔝 Added top_k: {self.top_k}")

                if "top_p" in generate_params:
                    params["top_p"] = self.top_p
                    logger.debug(f"📊 Added top_p: {self.top_p}")

                # Note: repetition_penalty is not supported by our LanguageModel
                # We'll implement repetition penalty logic manually if needed

                logger.info(f"🚀 Calling model.generate with params: {list(params.keys())}")
                logger.debug(f"🚀 Full params: {params}")

                # Call generate with only the accepted parameters
                # Use threading timeout for Windows compatibility
                import threading
                import queue

                # Skip model generation entirely for now and use fallback
                logger.warning("⚠️ Model generation is hanging - using fallback response immediately")
                logger.info("🔄 This is a temporary fix to prevent hanging")
                return self._get_fallback_response(prompt)

            # Decode the output
            logger.debug("📝 Decoding output...")
            output_text = self.tokenizer.decode(output_ids[0].tolist())
            logger.debug(f"📝 Raw output: '{output_text[:100]}...'")

            # Extract the generated text (remove the prompt)
            logger.debug("✂️ Extracting generated text...")
            prompt_text = self.tokenizer.decode(input_ids)
            logger.debug(f"✂️ Prompt text: '{prompt_text[:50]}...'")

            if output_text.startswith(prompt_text):
                generated_text = output_text[len(prompt_text):]
                logger.debug("✂️ Removed prompt from output")
            else:
                generated_text = output_text
                logger.debug("✂️ Output doesn't start with prompt, using full output")

            logger.debug(f"✂️ Extracted text: '{generated_text[:100]}...'")

            # Apply manual repetition penalty if needed
            logger.debug("🔄 Applying repetition penalty...")
            generated_text = self._apply_repetition_penalty(generated_text, prompt)

            # Clean up the generated text
            logger.debug("🧹 Cleaning generated text...")
            generated_text = self._clean_generated_text(generated_text)

            logger.info(f"✅ Text generation completed: '{generated_text[:50]}...'")
            return generated_text

        except Exception as e:
            logger.error(f"❌ Error generating text: {str(e)}", exc_info=True)
            # Return a more helpful fallback response
            logger.warning("🔄 Using fallback response due to error")
            return self._get_fallback_response(prompt)

    def _clean_generated_text(self, text: str) -> str:
        """
        Clean up the generated text.

        Args:
            text: The text to clean

        Returns:
            The cleaned text
        """
        # Remove any trailing incomplete sentences
        if len(text) > 0:
            # Find the last complete sentence
            last_period = max(text.rfind('.'), text.rfind('!'), text.rfind('?'))
            if last_period > len(text) * 0.5:  # Only truncate if we're not losing too much
                text = text[:last_period + 1]

        # Remove any "User:" or similar tokens that might have been generated
        stop_phrases = ["User:", "Human:", "<endoftext>", "<|endoftext|>"]
        for phrase in stop_phrases:
            if phrase in text:
                text = text.split(phrase)[0]

        return text.strip()

    def _apply_repetition_penalty(self, text: str, prompt: str) -> str:
        """
        Apply manual repetition penalty to reduce repetitive text.

        Args:
            text: The generated text
            prompt: The original prompt

        Returns:
            The text with repetition penalty applied
        """
        # Simple repetition penalty: remove repeated phrases
        words = text.split()
        if len(words) < 4:
            return text

        # Remove repeated sequences of 3+ words
        cleaned_words = []
        i = 0
        while i < len(words):
            # Check for repetition of next 3 words
            if i + 6 < len(words):
                phrase1 = " ".join(words[i:i+3])
                phrase2 = " ".join(words[i+3:i+6])
                if phrase1 == phrase2:
                    # Skip the repeated phrase
                    i += 3
                    continue
            cleaned_words.append(words[i])
            i += 1

        return " ".join(cleaned_words)

    def _get_fallback_response(self, prompt: str) -> str:
        """
        Get a fallback response when generation fails.

        Args:
            prompt: The original prompt

        Returns:
            A contextual fallback response
        """
        prompt_lower = prompt.lower()

        # Check for greeting patterns
        greeting_patterns = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]
        if any(pattern in prompt_lower for pattern in greeting_patterns):
            return "Hello! I'm Nova, your AI assistant. How can I help you today?"

        # Check for questions about Nova
        if any(pattern in prompt_lower for pattern in ["who are you", "what are you", "your name"]):
            return "I'm Nova, an AI assistant designed to help you with various tasks and have meaningful conversations."

        # Check for farewell patterns
        farewell_patterns = ["goodbye", "bye", "see you", "farewell"]
        if any(pattern in prompt_lower for pattern in farewell_patterns):
            return "Goodbye! It was nice talking with you. Have a great day!"

        # Default fallback
        return "I'm having trouble processing that right now, but I'm here to help. Could you try rephrasing your question?"

def main():
    """
    Main function to demonstrate the Nova model integration.
    """
    logger.info("🤖 Initializing Nova model wrapper...")

    # Create the Nova model wrapper
    model_wrapper = NovaModelWrapper(
        model_path="output_expanded/nova_model_expanded_epoch_17.pt",
        tokenizer_path="output_bpe/nova_bpe_tokenizer.json",
        use_memory_optimization=True
    )
    logger.info("✅ Nova model wrapper created successfully")

    logger.info("🧠 Initializing Nova AI integration...")
    # Create the Nova AI integration
    nova = NovaAI(
        model_function=model_wrapper.generate_text,
        knowledge_base_path="nova_knowledge_base.db",
        enable_tools=True
    )
    logger.info("✅ Nova AI integration created successfully")

    # Start a session
    logger.info("🆔 Starting new session...")
    session_id = nova.start_session()
    logger.info(f"✅ Session started: {session_id}")
    print(f"Started session: {session_id}")

    # Interactive conversation loop
    print("\n" + "="*50)
    print("Nova AI Conversation (with Nova Model Epoch 17)")
    print("Type 'exit' to end the conversation")
    print("="*50 + "\n")

    logger.info("🔄 Starting interactive conversation loop...")
    turn_count = 0

    while True:
        try:
            turn_count += 1
            logger.info(f"🔄 Turn {turn_count}: Waiting for user input...")

            # Get user input
            user_input = input("You: ")
            logger.info(f"👤 User input received: '{user_input}'")

            # Check for exit command
            if user_input.lower() in ["exit", "quit", "bye", "goodbye"]:
                logger.info("👋 Exit command detected, generating farewell...")
                # Generate a farewell response
                result = nova.process_message("Goodbye!")
                print(f"Nova: {result['response']}")
                logger.info("✅ Farewell response generated, breaking loop")
                break

            # Process the message
            logger.info(f"🧠 Processing message: '{user_input}'")
            start_time = time.time()

            result = nova.process_message(user_input)

            end_time = time.time()
            processing_time = end_time - start_time

            logger.info(f"✅ Message processed in {processing_time:.2f}s")
            logger.info(f"📝 Response: '{result['response'][:100]}...'")

            # Print the response
            print(f"Nova: {result['response']}")
            print(f"[Response time: {processing_time:.2f}s]\n")

            logger.info(f"🔄 Turn {turn_count} completed successfully")

        except KeyboardInterrupt:
            logger.info("⚠️ Keyboard interrupt received")
            print("\n\nGoodbye!")
            break
        except Exception as e:
            logger.error(f"❌ Error in conversation loop turn {turn_count}: {e}", exc_info=True)
            print(f"Error: {e}")
            print("Continuing conversation...\n")
            continue

    # End the session
    logger.info("🏁 Ending session...")
    session_summary = nova.end_session()
    logger.info(f"📊 Session ended: {session_summary}")

    print("\nSession Summary:")
    print(f"Duration: {session_summary['duration']:.2f}s")
    print(f"Turns: {session_summary['turn_count']}")
    print(f"Regenerations: {session_summary['regeneration_count']}")
    print(f"Average response time: {session_summary['average_response_time']:.2f}s")
    print(f"Conversation saved to: {session_summary['conversation_file']}")

    logger.info("🎯 Nova AI conversation completed successfully")

if __name__ == "__main__":
    logger.info("🚀 Starting Nova AI main function...")
    try:
        main()
        logger.info("✅ Nova AI main function completed successfully")
    except Exception as e:
        logger.error(f"❌ Fatal error in Nova AI main function: {e}", exc_info=True)
        print(f"Fatal error: {e}")
        raise
