2025-05-20 17:12:16 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:12:16 - __main__ - INFO - Loading Nova model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:12:16 - ai_brain_model.model.model_loader - INFO - Loading tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-20 17:12:16 - ai_brain_model.model.model_loader - INFO - Successfully loaded tokenizer with vocabulary size 3516
2025-05-20 17:12:16 - __main__ - INFO - Loaded tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-20 17:12:16 - ai_brain_model.model.model_loader - INFO - Loading model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:12:55 - ai_brain_model.model.model_loader - INFO - Loaded model from epoch 16
2025-05-20 17:12:56 - ai_brain_model.model.model_loader - INFO - Successfully loaded model with 192,927,744 parameters
2025-05-20 17:12:56 - __main__ - INFO - Loaded model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:12:56 - __main__ - INFO - Initializing memory optimization
2025-05-20 17:12:56 - ai_brain_model.utils.memory_optimization - INFO - CUDA is not available. Using CPU.
2025-05-20 17:12:56 - ai_brain_model.utils.memory_optimization - INFO - Quantizing model using dynamic quantization with 8 bits
2025-05-20 17:13:13 - ai_brain_model.utils.memory_optimization - INFO - Model quantized using dynamic quantization
2025-05-20 17:13:13 - __main__ - INFO - Memory optimization applied to model
2025-05-20 17:13:13 - ai_brain_model.utils.memory_optimization - INFO - Initialized KV cache with max_cache_size=10, max_seq_length=2048, device=cpu, use_memory_efficient=True
2025-05-20 17:13:13 - __main__ - INFO - Nova model wrapper initialized successfully
2025-05-20 17:13:13 - nova_integration - INFO - Initializing knowledge base from nova_knowledge_base.db
2025-05-20 17:13:14 - ai_brain_model.knowledge_base.vector_store - INFO - Loaded 107 vectors from vector_store.pkl
2025-05-20 17:13:14 - ai_brain_model.knowledge_base.vector_store - INFO - Initialized vector store with dimension 768
2025-05-20 17:13:14 - ai_brain_model.knowledge_base.vector_store - INFO - Vector store contains 107 vectors
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Database initialized
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Initialized knowledge base with database at nova_knowledge_base.db
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.knowledge_base - INFO - Vector dimension: 768, Using spaCy: True, spaCy model: en_core_web_md
2025-05-20 17:13:26 - nova_integration - INFO - Initializing tools
2025-05-20 17:13:26 - ai_brain_model.utils.config - WARNING - Configuration file config.json not found. Using defaults.
2025-05-20 17:13:26 - ai_brain_model.utils.config - INFO - Loaded API keys from Keys.txt
2025-05-20 17:13:26 - ai_brain_model.utils.config - INFO - Configuration manager initialized
2025-05-20 17:13:26 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-20 17:13:26 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-20 17:13:26 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-20 17:13:26 - ai_brain_model.utils.tools.web_search_tool - WARNING - Google Custom Search API key not found. Web search will be limited.
2025-05-20 17:13:26 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-20 17:13:26 - ai_brain_model.utils.tools.weather_tool - WARNING - WeatherAPI.com API key not found. Weather tool will be limited to mock data.
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO - Registering 7 tools:
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - web_search: Search the web for current information
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - wikipedia: Get information from Wikipedia
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - app_control: Open, close, or interact with Windows applications
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - system_info: Get information about the Windows system
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - browser: Control web browser actions
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - time_date: Get current time, date, or set reminders
2025-05-20 17:13:26 - ai_brain_model.utils.tools.tool_registry - INFO -   - weather: Get current weather for a location
2025-05-20 17:13:26 - nova_integration - INFO - Initialized 7 tools
2025-05-20 17:13:26 - nova_integration - INFO - Initializing conversation handler
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-20 17:13:26 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-20 17:13:26 - ai_brain_model.conversation.conversation_manager - INFO - Initialized conversation manager
2025-05-20 17:13:26 - ai_brain_model.conversation.response_enhancer - INFO - Initialized response enhancer
2025-05-20 17:13:27 - ai_brain_model.conversation.dynamic_prompting - INFO - Initialized prompt generator
2025-05-20 17:13:27 - ai_brain_model.conversation.self_evaluation - INFO - Initialized response evaluator
2025-05-20 17:13:27 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool selector with 7 available tools
2025-05-20 17:13:27 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool integrator with 7 available tools
2025-05-20 17:13:27 - ai_brain_model.conversation.conversation_handler - INFO - Initialized conversation handler
2025-05-20 17:13:27 - nova_integration - INFO - Nova AI initialized successfully
2025-05-20 17:13:27 - nova_integration - INFO - Started new session 789c07da-b29a-4fd6-bb7a-e6210e63ae9c for user anonymous
2025-05-20 17:14:03 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: hi, how you doing?
2025-05-20 17:14:03 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-20 17:14:03 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:14:03 - __main__ - ERROR - Error generating text: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AI Brain\nova_model_integration.py", line 148, in generate_text
    output_ids = self.model.generate(
                 ^^^^^^^^^^^^^^^^^^^^
TypeError: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
2025-05-20 17:14:03 - ai_brain_model.conversation.conversation_handler - INFO - Generated response in 0.31s (Turn #1)
2025-05-20 17:14:04 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: hi, how you doing?
2025-05-20 17:14:04 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-20 17:14:04 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:14:04 - nova_integration - INFO - Processed message in 0.71s (Session: 789c07da-b29a-4fd6-bb7a-e6210e63ae9c, State: greeting)
2025-05-20 17:16:13 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: whats your name?
2025-05-20 17:16:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-20 17:16:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:16:14 - __main__ - ERROR - Error generating text: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AI Brain\nova_model_integration.py", line 148, in generate_text
    output_ids = self.model.generate(
                 ^^^^^^^^^^^^^^^^^^^^
TypeError: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
2025-05-20 17:16:14 - ai_brain_model.conversation.conversation_handler - INFO - Generated response in 0.38s (Turn #2)
2025-05-20 17:16:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: whats your name?
2025-05-20 17:16:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: small_talk
2025-05-20 17:16:14 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:16:14 - nova_integration - INFO - Processed message in 0.84s (Session: 789c07da-b29a-4fd6-bb7a-e6210e63ae9c, State: small_talk)
2025-05-20 17:18:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: Goodbye!
2025-05-20 17:18:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: small_talk
2025-05-20 17:18:18 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:18:18 - __main__ - ERROR - Error generating text: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AI Brain\nova_model_integration.py", line 148, in generate_text
    import inspect
                 ^
TypeError: LanguageModel.generate() got an unexpected keyword argument 'repetition_penalty'
2025-05-20 17:18:18 - ai_brain_model.conversation.conversation_handler - INFO - Generated response in 0.30s (Turn #3)
2025-05-20 17:18:20 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: Goodbye!
2025-05-20 17:18:20 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: farewell
2025-05-20 17:18:20 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-20 17:18:20 - nova_integration - INFO - Processed message in 2.13s (Session: 789c07da-b29a-4fd6-bb7a-e6210e63ae9c, State: farewell)
2025-05-20 17:18:20 - ai_brain_model.conversation.conversation_manager - INFO - Saved conversation to conversations\conversation_789c07da-b29a-4fd6-bb7a-e6210e63ae9c.json
2025-05-20 17:18:20 - nova_integration - INFO - Ended session 789c07da-b29a-4fd6-bb7a-e6210e63ae9c after 293.40s with 3 turns
2025-05-20 17:21:01 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-20 17:21:01 - __main__ - INFO - Loading Nova model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:21:01 - ai_brain_model.model.model_loader - INFO - Loading tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-20 17:21:01 - ai_brain_model.model.model_loader - INFO - Successfully loaded tokenizer with vocabulary size 3516
2025-05-20 17:21:01 - __main__ - INFO - Loaded tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-20 17:21:01 - ai_brain_model.model.model_loader - INFO - Loading model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:21:46 - ai_brain_model.model.model_loader - INFO - Loaded model from epoch 16
2025-05-20 17:21:46 - ai_brain_model.model.model_loader - INFO - Successfully loaded model with 192,927,744 parameters
2025-05-20 17:21:47 - __main__ - INFO - Loaded model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-20 17:21:47 - __main__ - INFO - Initializing memory optimization
2025-05-20 17:21:47 - ai_brain_model.utils.memory_optimization - INFO - CUDA is not available. Using CPU.
2025-05-20 17:21:47 - ai_brain_model.utils.memory_optimization - INFO - Quantizing model using dynamic quantization with 8 bits
2025-05-20 17:22:03 - ai_brain_model.utils.memory_optimization - INFO - Model quantized using dynamic quantization
2025-05-20 17:22:03 - __main__ - INFO - Memory optimization applied to model
2025-05-20 17:22:03 - ai_brain_model.utils.memory_optimization - INFO - Initialized KV cache with max_cache_size=10, max_seq_length=2048, device=cpu, use_memory_efficient=True
2025-05-20 17:22:03 - __main__ - INFO - Nova model wrapper initialized successfully
2025-05-20 17:22:03 - nova_integration - INFO - Initializing knowledge base from nova_knowledge_base.db
2025-05-20 17:22:04 - ai_brain_model.knowledge_base.vector_store - INFO - Loaded 107 vectors from vector_store.pkl
2025-05-20 17:22:04 - ai_brain_model.knowledge_base.vector_store - INFO - Initialized vector store with dimension 768
2025-05-20 17:22:04 - ai_brain_model.knowledge_base.vector_store - INFO - Vector store contains 107 vectors
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Database initialized
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Initialized knowledge base with database at nova_knowledge_base.db
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.knowledge_base - INFO - Vector dimension: 768, Using spaCy: True, spaCy model: en_core_web_md
2025-05-20 17:22:15 - nova_integration - INFO - Initializing tools
2025-05-20 17:22:15 - ai_brain_model.utils.config - WARNING - Configuration file config.json not found. Using defaults.
2025-05-20 17:22:15 - ai_brain_model.utils.config - INFO - Loaded API keys from Keys.txt
2025-05-20 17:22:15 - ai_brain_model.utils.config - INFO - Configuration manager initialized
2025-05-20 17:22:15 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-20 17:22:15 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-20 17:22:15 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-20 17:22:15 - ai_brain_model.utils.tools.web_search_tool - WARNING - Google Custom Search API key not found. Web search will be limited.
2025-05-20 17:22:15 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-20 17:22:15 - ai_brain_model.utils.tools.weather_tool - WARNING - WeatherAPI.com API key not found. Weather tool will be limited to mock data.
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO - Registering 7 tools:
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - web_search: Search the web for current information
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - wikipedia: Get information from Wikipedia
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - app_control: Open, close, or interact with Windows applications
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - system_info: Get information about the Windows system
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - browser: Control web browser actions
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - time_date: Get current time, date, or set reminders
2025-05-20 17:22:15 - ai_brain_model.utils.tools.tool_registry - INFO -   - weather: Get current weather for a location
2025-05-20 17:22:15 - nova_integration - INFO - Initialized 7 tools
2025-05-20 17:22:15 - nova_integration - INFO - Initializing conversation handler
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-20 17:22:15 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-20 17:22:15 - ai_brain_model.conversation.conversation_manager - INFO - Initialized conversation manager
2025-05-20 17:22:15 - ai_brain_model.conversation.response_enhancer - INFO - Initialized response enhancer
2025-05-20 17:22:15 - ai_brain_model.conversation.dynamic_prompting - INFO - Initialized prompt generator
2025-05-20 17:22:16 - ai_brain_model.conversation.self_evaluation - INFO - Initialized response evaluator
2025-05-20 17:22:16 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool selector with 7 available tools
2025-05-20 17:22:16 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool integrator with 7 available tools
2025-05-20 17:22:16 - ai_brain_model.conversation.conversation_handler - INFO - Initialized conversation handler
2025-05-20 17:22:16 - nova_integration - INFO - Nova AI initialized successfully
2025-05-20 17:22:16 - nova_integration - INFO - Started new session 4d54a29e-d645-46a6-8f4c-eaca8c54c2f0 for user anonymous
2025-05-20 17:23:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: Hi, How you doing?
2025-05-20 17:23:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-20 17:23:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-29 17:31:15 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-29 17:31:15 - __main__ - INFO - Loading Nova model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-29 17:31:15 - ai_brain_model.model.model_loader - INFO - Loading tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-29 17:31:15 - ai_brain_model.model.model_loader - INFO - Successfully loaded tokenizer with vocabulary size 3516
2025-05-29 17:31:15 - __main__ - INFO - Loaded tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-29 17:31:15 - ai_brain_model.model.model_loader - INFO - Loading model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-29 17:34:21 - ai_brain_model.model.model_loader - INFO - Loaded model from epoch 16
2025-05-29 17:34:22 - ai_brain_model.model.model_loader - INFO - Successfully loaded model with 192,927,744 parameters
2025-05-29 17:34:26 - __main__ - INFO - Loaded model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-29 17:34:26 - __main__ - INFO - Initializing memory optimization
2025-05-29 17:34:26 - ai_brain_model.utils.memory_optimization - INFO - CUDA is not available. Using CPU.
2025-05-29 17:34:26 - ai_brain_model.utils.memory_optimization - INFO - Quantizing model using dynamic quantization with 8 bits
2025-05-29 17:35:31 - ai_brain_model.utils.memory_optimization - INFO - Model quantized using dynamic quantization
2025-05-29 17:35:31 - __main__ - INFO - Memory optimization applied to model
2025-05-29 17:35:31 - ai_brain_model.utils.memory_optimization - INFO - Initialized KV cache with max_cache_size=10, max_seq_length=2048, device=cpu, use_memory_efficient=True
2025-05-29 17:35:31 - __main__ - INFO - Nova model wrapper initialized successfully
2025-05-29 17:35:33 - nova_integration - INFO - Initializing knowledge base from nova_knowledge_base.db
2025-05-29 17:35:34 - ai_brain_model.knowledge_base.vector_store - INFO - Loaded 107 vectors from vector_store.pkl
2025-05-29 17:35:34 - ai_brain_model.knowledge_base.vector_store - INFO - Initialized vector store with dimension 768
2025-05-29 17:35:34 - ai_brain_model.knowledge_base.vector_store - INFO - Vector store contains 107 vectors
2025-05-29 17:36:07 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-29 17:36:07 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-29 17:36:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Database initialized
2025-05-29 17:36:07 - ai_brain_model.knowledge_base.knowledge_base - INFO - Initialized knowledge base with database at nova_knowledge_base.db
2025-05-29 17:36:08 - ai_brain_model.knowledge_base.knowledge_base - INFO - Vector dimension: 768, Using spaCy: True, spaCy model: en_core_web_md
2025-05-29 17:36:08 - nova_integration - INFO - Initializing tools
2025-05-29 17:36:08 - ai_brain_model.utils.config - WARNING - Configuration file config.json not found. Using defaults.
2025-05-29 17:36:08 - ai_brain_model.utils.config - INFO - Loaded API keys from Keys.txt
2025-05-29 17:36:08 - ai_brain_model.utils.config - INFO - Configuration manager initialized
2025-05-29 17:36:08 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-29 17:36:08 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-29 17:36:08 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-29 17:36:08 - ai_brain_model.utils.tools.web_search_tool - WARNING - Google Custom Search API key not found. Web search will be limited.
2025-05-29 17:36:08 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-29 17:36:08 - ai_brain_model.utils.tools.weather_tool - WARNING - WeatherAPI.com API key not found. Weather tool will be limited to mock data.
2025-05-29 17:36:08 - ai_brain_model.utils.tools.tool_registry - INFO - Registering 7 tools:
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - web_search: Search the web for current information
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - wikipedia: Get information from Wikipedia
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - app_control: Open, close, or interact with Windows applications
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - system_info: Get information about the Windows system
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - browser: Control web browser actions
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - time_date: Get current time, date, or set reminders
2025-05-29 17:36:09 - ai_brain_model.utils.tools.tool_registry - INFO -   - weather: Get current weather for a location
2025-05-29 17:36:09 - nova_integration - INFO - Initialized 7 tools
2025-05-29 17:36:09 - nova_integration - INFO - Initializing conversation handler
2025-05-29 17:36:09 - ai_brain_model.knowledge_base.entity_extractor - WARNING - spaCy model en_core_web_md not found. Falling back to rule-based extraction.
2025-05-29 17:36:09 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-29 17:36:09 - ai_brain_model.conversation.conversation_manager - INFO - Initialized conversation manager
2025-05-29 17:36:09 - ai_brain_model.conversation.response_enhancer - INFO - Initialized response enhancer
2025-05-29 17:36:09 - ai_brain_model.conversation.dynamic_prompting - INFO - Initialized prompt generator
2025-05-29 17:36:09 - ai_brain_model.conversation.self_evaluation - INFO - Initialized response evaluator
2025-05-29 17:36:09 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool selector with 7 available tools
2025-05-29 17:36:09 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool integrator with 7 available tools
2025-05-29 17:36:09 - ai_brain_model.conversation.conversation_handler - INFO - Initialized conversation handler
2025-05-29 17:36:09 - nova_integration - INFO - Nova AI initialized successfully
2025-05-29 17:36:09 - nova_integration - INFO - Started new session 0919e9d1-008d-4d29-a417-4aa27564ce46 for user anonymous
2025-05-29 17:36:41 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: Hello, how you doing today?
2025-05-29 17:36:41 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-29 17:36:41 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
2025-05-30 19:50:11 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:08:57 - root - INFO - Logging configured with level=info, log_to_console=True, log_to_file=True
2025-05-30 20:08:57 - __main__ - INFO - Loading Nova model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-30 20:08:57 - ai_brain_model.model.model_loader - INFO - Loading tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-30 20:08:57 - ai_brain_model.model.model_loader - INFO - Successfully loaded tokenizer with vocabulary size 3516
2025-05-30 20:08:57 - __main__ - INFO - Loaded tokenizer from output_bpe/nova_bpe_tokenizer.json
2025-05-30 20:08:57 - ai_brain_model.model.model_loader - INFO - Loading model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-30 20:09:58 - ai_brain_model.model.model_loader - INFO - Loaded model from epoch 16
2025-05-30 20:09:59 - ai_brain_model.model.model_loader - INFO - Successfully loaded model with 192,927,744 parameters
2025-05-30 20:10:00 - __main__ - INFO - Loaded model from output_expanded/nova_model_expanded_epoch_17.pt
2025-05-30 20:10:00 - __main__ - INFO - Initializing memory optimization
2025-05-30 20:10:00 - ai_brain_model.utils.memory_optimization - INFO - CUDA is not available. Using CPU.
2025-05-30 20:10:00 - ai_brain_model.utils.memory_optimization - INFO - Quantizing model using dynamic quantization with 8 bits
2025-05-30 20:10:40 - ai_brain_model.utils.memory_optimization - INFO - Model quantized using dynamic quantization
2025-05-30 20:10:40 - __main__ - INFO - Memory optimization applied to model
2025-05-30 20:10:40 - ai_brain_model.utils.memory_optimization - INFO - Initialized KV cache with max_cache_size=10, max_seq_length=2048, device=cpu, use_memory_efficient=True
2025-05-30 20:10:40 - __main__ - INFO - Nova model wrapper initialized successfully
2025-05-30 20:10:41 - nova_integration - INFO - Initializing knowledge base from nova_knowledge_base.db
2025-05-30 20:10:44 - ai_brain_model.knowledge_base.vector_store - INFO - Loaded 107 vectors from vector_store.pkl
2025-05-30 20:10:44 - ai_brain_model.knowledge_base.vector_store - INFO - Initialized vector store with dimension 768
2025-05-30 20:10:44 - ai_brain_model.knowledge_base.vector_store - INFO - Vector store contains 107 vectors
2025-05-30 20:11:10 - ai_brain_model.knowledge_base.entity_extractor - INFO - Loaded spaCy model en_core_web_md
2025-05-30 20:11:10 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-30 20:11:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Database initialized
2025-05-30 20:11:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Initialized knowledge base with database at nova_knowledge_base.db
2025-05-30 20:11:10 - ai_brain_model.knowledge_base.knowledge_base - INFO - Vector dimension: 768, Using spaCy: True, spaCy model: en_core_web_md
2025-05-30 20:11:10 - nova_integration - INFO - Initializing tools
2025-05-30 20:11:10 - ai_brain_model.utils.config - WARNING - Configuration file config.json not found. Using defaults.
2025-05-30 20:11:10 - ai_brain_model.utils.config - INFO - Loaded API keys from Keys.txt
2025-05-30 20:11:10 - ai_brain_model.utils.config - INFO - Configuration manager initialized
2025-05-30 20:11:10 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-30 20:11:10 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-30 20:11:10 - ai_brain_model.utils.config - WARNING - API key for Google Custom search API not found
2025-05-30 20:11:10 - ai_brain_model.utils.tools.web_search_tool - WARNING - Google Custom Search API key not found. Web search will be limited.
2025-05-30 20:11:10 - ai_brain_model.utils.config - WARNING - API key for WeatherAPI.com not found
2025-05-30 20:11:10 - ai_brain_model.utils.tools.weather_tool - WARNING - WeatherAPI.com API key not found. Weather tool will be limited to mock data.
2025-05-30 20:11:10 - ai_brain_model.utils.tools.tool_registry - INFO - Registering 7 tools:
2025-05-30 20:11:10 - ai_brain_model.utils.tools.tool_registry - INFO -   - web_search: Search the web for current information
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - wikipedia: Get information from Wikipedia
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - app_control: Open, close, or interact with Windows applications
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - system_info: Get information about the Windows system
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - browser: Control web browser actions
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - time_date: Get current time, date, or set reminders
2025-05-30 20:11:11 - ai_brain_model.utils.tools.tool_registry - INFO -   - weather: Get current weather for a location
2025-05-30 20:11:11 - nova_integration - INFO - Initialized 7 tools
2025-05-30 20:11:11 - nova_integration - INFO - Initializing conversation handler
2025-05-30 20:11:14 - ai_brain_model.knowledge_base.entity_extractor - INFO - Loaded spaCy model en_core_web_md
2025-05-30 20:11:14 - ai_brain_model.knowledge_base.entity_extractor - INFO - Initialized entity extractor (use_spacy=True)
2025-05-30 20:11:14 - ai_brain_model.conversation.conversation_manager - INFO - Initialized conversation manager
2025-05-30 20:11:14 - ai_brain_model.conversation.response_enhancer - INFO - Initialized response enhancer
2025-05-30 20:11:14 - ai_brain_model.conversation.dynamic_prompting - INFO - Initialized prompt generator
2025-05-30 20:11:14 - ai_brain_model.conversation.self_evaluation - INFO - Initialized response evaluator
2025-05-30 20:11:14 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool selector with 7 available tools
2025-05-30 20:11:14 - ai_brain_model.conversation.tool_integration - INFO - Initialized tool integrator with 7 available tools
2025-05-30 20:11:14 - ai_brain_model.conversation.conversation_handler - INFO - Initialized conversation handler
2025-05-30 20:11:14 - nova_integration - INFO - Nova AI initialized successfully
2025-05-30 20:11:14 - nova_integration - INFO - Started new session 443d0ca3-8554-4de4-84c0-3250d763800b for user anonymous
2025-05-30 20:12:53 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 3 results for query: hi, how you doing today?
2025-05-30 20:12:54 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: greeting
2025-05-30 20:12:54 - ai_brain_model.knowledge_base.knowledge_base - INFO - Found 0 results for entity: Nova
